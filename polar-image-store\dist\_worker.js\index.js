globalThis.process ??= {}; globalThis.process.env ??= {};
import { renderers } from './renderers.mjs';
import { c as createExports } from './chunks/server_DFjLKER-.mjs';
import { manifest } from './manifest_CQJbfJhX.mjs';

const serverIslandMap = new Map();;

const _page0 = () => import('./pages/_image.astro.mjs');
const _page1 = () => import('./pages/about.astro.mjs');
const _page2 = () => import('./pages/api/checkout.astro.mjs');
const _page3 = () => import('./pages/api/image-proxy.astro.mjs');
const _page4 = () => import('./pages/api/products.astro.mjs');
const _page5 = () => import('./pages/api/search.astro.mjs');
const _page6 = () => import('./pages/api/tags.astro.mjs');
const _page7 = () => import('./pages/api/webhooks.astro.mjs');
const _page8 = () => import('./pages/privacy.astro.mjs');
const _page9 = () => import('./pages/products/category/_category_.astro.mjs');
const _page10 = () => import('./pages/products/tag/_tag_.astro.mjs');
const _page11 = () => import('./pages/products/_slug_.astro.mjs');
const _page12 = () => import('./pages/products.astro.mjs');
const _page13 = () => import('./pages/success.astro.mjs');
const _page14 = () => import('./pages/terms.astro.mjs');
const _page15 = () => import('./pages/index.astro.mjs');
const pageMap = new Map([
    ["node_modules/astro/dist/assets/endpoint/generic.js", _page0],
    ["src/pages/about.astro", _page1],
    ["src/pages/api/checkout.ts", _page2],
    ["src/pages/api/image-proxy.ts", _page3],
    ["src/pages/api/products.ts", _page4],
    ["src/pages/api/search.ts", _page5],
    ["src/pages/api/tags.ts", _page6],
    ["src/pages/api/webhooks.ts", _page7],
    ["src/pages/privacy.astro", _page8],
    ["src/pages/products/category/[category].astro", _page9],
    ["src/pages/products/tag/[tag].astro", _page10],
    ["src/pages/products/[slug].astro", _page11],
    ["src/pages/products/index.astro", _page12],
    ["src/pages/success.astro", _page13],
    ["src/pages/terms.astro", _page14],
    ["src/pages/index.astro", _page15]
]);

const _manifest = Object.assign(manifest, {
    pageMap,
    serverIslandMap,
    renderers,
    actions: () => import('./_noop-actions.mjs'),
    middleware: () => import('./_astro-internal_middleware.mjs')
});
const _exports = createExports(_manifest);
const __astrojsSsrVirtualEntry = _exports.default;

export { __astrojsSsrVirtualEntry as default, pageMap };
